# Application Configuration
spring:
  application:
    name: demo
  
  # Database Configuration
  datasource:
    url: ****************************************
    username: demo_user
    password: demo_password
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
  
  # JPA/Hibernate Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
  
  # H2 Database for Development (uncomment to use)
  # datasource:
  #   url: jdbc:h2:mem:testdb
  #   driver-class-name: org.h2.Driver
  # h2:
  #   console:
  #     enabled: true
  
  # Security Configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: http://localhost:8080/realms/demo-realm/protocol/openid_connect/certs
          issuer-uri: http://localhost:8080/realms/demo-realm

# Server Configuration
server:
  port: 8080

# Keycloak Admin Configuration
keycloak:
  auth-server-url: http://localhost:8080
  realm: demo-realm
  resource: demo-client
  credentials:
    secret: your-client-secret

# Logging Configuration
logging:
  level:
    com.thanhnb.demo: INFO
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.springframework.web.filter.CommonsRequestLoggingFilter: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{requestId}] [%X{username}] %logger{36} - %msg%n"

# Application Specific Configuration
app:
  internal-api:
    base-url: http://localhost:8080
    timeout: 5000
  external-api:
    timeout: 10000
    retry-attempts: 3
  rest-template:
    connection-timeout: 5000
    read-timeout: 10000

# Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when_authorized 