# Development Configuration with H2 Database
spring:
  # Server Configuration
  server:
    port: 8081

  # H2 Database Configuration for Development
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # H2 Console
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true
  
  # JPA/Hibernate Configuration for H2
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
  
  # Disable OAuth2 for development
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.OAuth2ResourceServerAutoConfiguration

# Logging Configuration for Development
logging:
  level:
    com.thanhnb.demo: DEBUG
    org.springframework.security: INFO
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{requestId}] [%X{username}] %logger{36} - %msg%n"

# Application Specific Configuration for Development
app:
  # Security Configuration - DISABLED for development
  security:
    enabled: false
    cors:
      enabled: true
      allowed-origins:
        - "http://localhost:3000"
        - "http://localhost:8080"
        - "http://localhost:4200"
      allowed-methods:
        - "*"
      allowed-headers:
        - "*"
      allow-credentials: true
      max-age: 3600

    # All endpoints are public in development
    public-endpoints:
      - "/**"

    # JWT Configuration - not needed in dev
    jwt:
      jwk-set-uri: ""
      issuer-uri: ""
