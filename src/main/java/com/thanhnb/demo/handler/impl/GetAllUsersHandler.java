package com.thanhnb.demo.handler.impl;

import com.thanhnb.demo.application.usecase.GetUserUseCase;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.handler.bus.BaseApiHandler;
import com.thanhnb.demo.presentation.dto.GetAllUsersRequest;
import com.thanhnb.demo.presentation.dto.UsersPageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

/**
 * Handler for getting all users through presentation layer
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class GetAllUsersHandler extends BaseApiHandler<GetAllUsersRequest, UsersPageResponse> {
    
    private final GetUserUseCase getUserUseCase;
    
    @Override
    public UsersPageResponse handle(GetAllUsersRequest request) {
        log.info("Handling get all users request");
        
        // Execute use case
        Page<User> users = getUserUseCase.getAll(request.pageable());
        
        // Convert domain page to presentation response
        UsersPageResponse response = UsersPageResponse.fromDomain(users);
        
        log.info("Successfully retrieved {} users", users.getTotalElements());
        return response;
    }
}
