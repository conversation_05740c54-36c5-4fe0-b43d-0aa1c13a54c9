package com.thanhnb.demo.handler.impl;

import com.thanhnb.demo.application.usecase.GetUserUseCase;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.handler.bus.BaseApiHandler;
import com.thanhnb.demo.presentation.dto.GetUserByIdRequest;
import com.thanhnb.demo.presentation.dto.UserResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Handler for getting user by ID through presentation layer
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class GetUserByIdHandler extends BaseApiHandler<GetUserByIdRequest, UserResponse> {
    
    private final GetUserUseCase getUserUseCase;
    
    @Override
    public UserResponse handle(GetUserByIdRequest request) {
        log.info("Handling get user by ID request for ID: {}", request.userId());
        
        // Execute use case
        User user = getUserUseCase.getById(request.userId());
        
        // Convert domain model to presentation response
        UserResponse response = UserResponse.fromDomain(user);
        
        log.info("Successfully retrieved user: {}", user.getUsername());
        return response;
    }
}
