package com.thanhnb.demo.handler.impl;

import com.thanhnb.demo.application.usecase.GetUserUseCase;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.handler.bus.ApiHandler;
import com.thanhnb.demo.handler.dto.request.GetAllUsersApiRequest;
import com.thanhnb.demo.handler.dto.response.UsersPageApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

/**
 * Handler for getting all users through API
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class GetAllUsersApiHandler implements ApiHandler<GetAllUsersApiRequest, UsersPageApiResponse> {
    
    private final GetUserUseCase getUserUseCase;
    
    @Override
    public UsersPageApiResponse handle(GetAllUsersApiRequest request) {
        log.info("Handling get all users API request");
        
        // Execute use case
        Page<User> users = getUserUseCase.getAll(request.getPageable());
        
        // Convert domain page to API response
        UsersPageApiResponse response = UsersPageApiResponse.fromDomain(users);
        
        log.info("Successfully retrieved {} users", users.getTotalElements());
        return response;
    }
    
    @Override
    public Class<GetAllUsersApiRequest> getRequestType() {
        return GetAllUsersApiRequest.class;
    }
}
