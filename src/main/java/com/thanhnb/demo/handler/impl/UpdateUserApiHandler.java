package com.thanhnb.demo.handler.impl;

import com.thanhnb.demo.application.usecase.UpdateUserUseCase;
import com.thanhnb.demo.application.dto.UpdateUserCommand;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.handler.bus.ApiHandler;
import com.thanhnb.demo.handler.dto.request.UpdateUserApiRequest;
import com.thanhnb.demo.handler.dto.response.UserApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Handler for updating users through API
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UpdateUserApiHandler implements ApiHandler<UpdateUserApiRequest, UserApiResponse> {
    
    private final UpdateUserUseCase updateUserUseCase;
    
    @Override
    public UserApiResponse handle(UpdateUserApiRequest request) {
        log.info("Handling update user API request for user ID: {}", request.getUserId());
        
        // Convert API request to use case command
        UpdateUserCommand command = UpdateUserCommand.of(
            request.getFirstName(),
            request.getLastName(),
            request.getPhoneNumber(),
            request.getStatus()
        );
        
        // Execute use case
        User user = updateUserUseCase.execute(request.getUserId(), command);
        
        // Convert domain model to API response
        UserApiResponse response = UserApiResponse.fromDomain(user);
        
        log.info("Successfully updated user: {}", user.getUsername());
        return response;
    }
}
