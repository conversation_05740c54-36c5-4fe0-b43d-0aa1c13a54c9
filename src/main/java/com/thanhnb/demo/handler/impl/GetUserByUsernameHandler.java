package com.thanhnb.demo.handler.impl;

import com.thanhnb.demo.application.usecase.GetUserUseCase;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.handler.bus.BaseApiHandler;
import com.thanhnb.demo.presentation.dto.GetUserByUsernameRequest;
import com.thanhnb.demo.presentation.dto.UserResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Handler for getting user by username through presentation layer
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class GetUserByUsernameHandler extends BaseApiHandler<GetUserByUsernameRequest, UserResponse> {
    
    private final GetUserUseCase getUserUseCase;
    
    @Override
    public UserResponse handle(GetUserByUsernameRequest request) {
        log.info("Handling get user by username request for username: {}", request.username());
        
        // Execute use case
        User user = getUserUseCase.getByUsername(request.username());
        
        // Convert domain model to presentation response
        UserResponse response = UserResponse.fromDomain(user);
        
        log.info("Successfully retrieved user: {}", user.getUsername());
        return response;
    }
}
