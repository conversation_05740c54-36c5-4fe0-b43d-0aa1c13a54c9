package com.thanhnb.demo.handler.impl;

import com.thanhnb.demo.application.usecase.CreateUserUseCase;
import com.thanhnb.demo.application.dto.CreateUserCommand;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.handler.bus.ApiHandler;
import com.thanhnb.demo.handler.dto.request.CreateUserApiRequest;
import com.thanhnb.demo.handler.dto.response.UserApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Handler for creating users through API
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CreateUserApiHandler implements ApiHandler<CreateUserApiRequest, UserApiResponse> {
    
    private final CreateUserUseCase createUserUseCase;
    
    @Override
    public UserApiResponse handle(CreateUserApiRequest request) {
        log.info("Handling create user API request for username: {}", request.getUsername());
        
        // Convert API request to use case command
        CreateUserCommand command = CreateUserCommand.of(
            request.getUsername(),
            request.getEmail(),
            request.getFirstName(),
            request.getLastName(),
            request.getPhoneNumber()
        );
        
        // Execute use case
        User user = createUserUseCase.execute(command);
        
        // Convert domain model to API response
        UserApiResponse response = UserApiResponse.fromDomain(user);
        
        log.info("Successfully created user: {}", user.getUsername());
        return response;
    }
    
    @Override
    public Class<CreateUserApiRequest> getRequestType() {
        return CreateUserApiRequest.class;
    }
}
