package com.thanhnb.demo.handler.impl;

import com.thanhnb.demo.application.usecase.CreateUserUseCase;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.handler.bus.BaseApiHandler;
import com.thanhnb.demo.presentation.dto.CreateUserRequest;
import com.thanhnb.demo.presentation.dto.UserResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Handler for creating users through presentation layer
 * Directly processes CreateUserRequest from REST API
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CreateUserHandler extends BaseApiHandler<CreateUserRequest, UserResponse> {
    
    private final CreateUserUseCase createUserUseCase;
    
    @Override
    public UserResponse handle(CreateUserRequest request) {
        log.info("Handling create user request for username: {}", request.username());
        
        // Execute use case using the toCommand() method
        User user = createUserUseCase.execute(request.toCommand());
        
        // Convert domain model to presentation response
        UserResponse response = UserResponse.fromDomain(user);
        
        log.info("Successfully created user: {}", user.getUsername());
        return response;
    }
}
