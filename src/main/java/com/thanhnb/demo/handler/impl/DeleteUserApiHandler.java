package com.thanhnb.demo.handler.impl;

import com.thanhnb.demo.application.usecase.DeleteUserUseCase;
import com.thanhnb.demo.handler.bus.ApiHandler;
import com.thanhnb.demo.handler.dto.request.DeleteUserApiRequest;
import com.thanhnb.demo.handler.dto.response.DeleteUserApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Handler for deleting users through API
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DeleteUserApiHandler implements ApiHandler<DeleteUserApiRequest, DeleteUserApiResponse> {
    
    private final DeleteUserUseCase deleteUserUseCase;
    
    @Override
    public DeleteUserApiResponse handle(DeleteUserApiRequest request) {
        log.info("Handling delete user API request for user ID: {}", request.getUserId());
        
        // Execute use case
        deleteUserUseCase.execute(request.getUserId());
        
        // Create API response
        DeleteUserApiResponse response = DeleteUserApiResponse.success(request.getUserId());
        
        log.info("Successfully deleted user with ID: {}", request.getUserId());
        return response;
    }
    
    @Override
    public Class<DeleteUserApiRequest> getRequestType() {
        return DeleteUserApiRequest.class;
    }
}
