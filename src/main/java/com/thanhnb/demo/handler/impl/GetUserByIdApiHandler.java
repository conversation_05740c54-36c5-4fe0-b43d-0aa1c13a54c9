package com.thanhnb.demo.handler.impl;

import com.thanhnb.demo.application.usecase.GetUserUseCase;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.handler.bus.ApiHandler;
import com.thanhnb.demo.handler.dto.request.GetUserByIdApiRequest;
import com.thanhnb.demo.handler.dto.response.UserApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Handler for getting user by ID through API
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class GetUserByIdApiHandler implements ApiHandler<GetUserByIdApiRequest, UserApiResponse> {
    
    private final GetUserUseCase getUserUseCase;
    
    @Override
    public UserApiResponse handle(GetUserByIdApiRequest request) {
        log.info("Handling get user by ID API request for user ID: {}", request.getUserId());
        
        // Execute use case
        User user = getUserUseCase.getById(request.getUserId());
        
        // Convert domain model to API response
        UserApiResponse response = UserApiResponse.fromDomain(user);
        
        log.info("Successfully retrieved user: {}", user.getUsername());
        return response;
    }
}
