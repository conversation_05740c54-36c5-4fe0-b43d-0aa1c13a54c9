package com.thanhnb.demo.handler.impl;

import com.thanhnb.demo.application.usecase.GetUserUseCase;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.handler.bus.ApiHandler;
import com.thanhnb.demo.handler.dto.request.GetUserByUsernameApiRequest;
import com.thanhnb.demo.handler.dto.response.UserApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Handler for getting user by username through API
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class GetUserByUsernameApiHandler implements <PERSON>piHandler<GetUserByUsernameApiRequest, UserApiResponse> {
    
    private final GetUserUseCase getUserUseCase;
    
    @Override
    public UserApiResponse handle(GetUserByUsernameApiRequest request) {
        log.info("Handling get user by username API request for username: {}", request.getUsername());
        
        // Execute use case
        User user = getUserUseCase.getByUsername(request.getUsername());
        
        // Convert domain model to API response
        UserApiResponse response = UserApiResponse.fromDomain(user);
        
        log.info("Successfully retrieved user: {}", user.getUsername());
        return response;
    }
    
    @Override
    public Class<GetUserByUsernameApiRequest> getRequestType() {
        return GetUserByUsernameApiRequest.class;
    }
}
