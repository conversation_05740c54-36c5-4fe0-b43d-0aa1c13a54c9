package com.thanhnb.demo.handler.impl;

import com.thanhnb.demo.application.usecase.GetUserUseCase;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.handler.bus.ApiHandler;
import com.thanhnb.demo.handler.dto.request.SearchUsersApiRequest;
import com.thanhnb.demo.handler.dto.response.UsersPageApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

/**
 * Handler for searching users through API
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SearchUsersApiHandler implements ApiHandler<SearchUsersApiRequest, UsersPageApiResponse> {
    
    private final GetUserUseCase getUserUseCase;
    
    @Override
    public UsersPageApiResponse handle(SearchUsersApiRequest request) {
        log.info("Handling search users API request with term: {}", request.getSearchTerm());
        
        // Execute use case
        Page<User> users = getUserUseCase.search(request.getSearchTerm(), request.getPageable());
        
        // Convert domain page to API response
        UsersPageApiResponse response = UsersPageApiResponse.fromDomain(users);
        
        log.info("Successfully found {} users for search term: {}", users.getTotalElements(), request.getSearchTerm());
        return response;
    }
    
    @Override
    public Class<SearchUsersApiRequest> getRequestType() {
        return SearchUsersApiRequest.class;
    }
}
