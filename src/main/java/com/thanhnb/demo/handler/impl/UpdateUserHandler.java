package com.thanhnb.demo.handler.impl;

import com.thanhnb.demo.application.usecase.UpdateUserUseCase;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.handler.bus.BaseApiHandler;
import com.thanhnb.demo.presentation.dto.UpdateUserWithIdRequest;
import com.thanhnb.demo.presentation.dto.UserResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Handler for updating users through presentation layer
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UpdateUserHandler extends BaseApiHandler<UpdateUserWithIdRequest, UserResponse> {
    
    private final UpdateUserUseCase updateUserUseCase;
    
    @Override
    public UserResponse handle(UpdateUserWithIdRequest request) {
        log.info("Handling update user request for user ID: {}", request.userId());
        
        // Execute use case
        User user = updateUserUseCase.execute(request.userId(), request.toCommand());
        
        // Convert domain model to presentation response
        UserResponse response = UserResponse.fromDomain(user);
        
        log.info("Successfully updated user: {}", user.getUsername());
        return response;
    }
}
