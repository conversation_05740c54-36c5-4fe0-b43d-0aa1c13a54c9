package com.thanhnb.demo.handler.config;

import com.thanhnb.demo.handler.bus.ApiHandler;
import com.thanhnb.demo.handler.bus.BaseApiHandler;
import com.thanhnb.demo.handler.bus.SpringBus;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;

/**
 * Configuration for auto-registering all API handlers with Spring Bus
 */
@Configuration
@RequiredArgsConstructor
@Slf4j
public class HandlerConfiguration {
    
    private final SpringBus springBus;
    private final ApplicationContext applicationContext;
    
    /**
     * Auto-register all ApiHandler beans with Spring Bus
     */
    @PostConstruct
    @SuppressWarnings({"unchecked", "rawtypes"})
    public void registerHandlers() {
        log.info("Starting API handler registration...");
        
        Map<String, ApiHandler> handlers = applicationContext.getBeansOfType(ApiHandler.class);
        
        for (Map.Entry<String, ApiHandler> entry : handlers.entrySet()) {
            ApiHandler handler = entry.getValue();
            Class<?> requestType = handler.getRequestType();
            
            springBus.registerHandler(requestType, handler);
            log.info("Registered API handler: {} for request type: {}", 
                    entry.getKey(), requestType.getSimpleName());
        }
        
        log.info("API handler registration completed. Total handlers registered: {}", handlers.size());
    }
}
