package com.thanhnb.demo.handler.bus;

/**
 * Interface for API handlers
 * Each handler processes a specific request type and returns a response
 * Request type is automatically detected using reflection
 */
public interface ApiHandler<TRequest, TResponse> {

    /**
     * Handle the API request and return response
     * @param request the API request
     * @return the API response
     */
    TResponse handle(TRequest request);
}
