package com.thanhnb.demo.handler.bus;

/**
 * Interface for API handlers
 * Each handler processes a specific request type and returns a response
 */
public interface ApiHandler<TRequest, TResponse> {
    
    /**
     * Handle the API request and return response
     * @param request the API request
     * @return the API response
     */
    TResponse handle(TRequest request);
    
    /**
     * Get the request type this handler supports
     * @return the request class type
     */
    Class<TRequest> getRequestType();
}
