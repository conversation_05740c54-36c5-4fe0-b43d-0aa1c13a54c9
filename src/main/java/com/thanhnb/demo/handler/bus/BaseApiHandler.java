package com.thanhnb.demo.handler.bus;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * Abstract base class for API handlers that automatically detects request type using reflection
 * Eliminates the need for manual getRequestType() implementation
 */
public abstract class BaseApiHandler<TRequest, TResponse> implements <PERSON><PERSON><PERSON><PERSON><PERSON><TRequest, TResponse> {
    
    private final Class<TRequest> requestType;
    
    @SuppressWarnings("unchecked")
    protected BaseApiHandler() {
        Type genericSuperclass = getClass().getGenericSuperclass();
        
        if (genericSuperclass instanceof ParameterizedType parameterizedType) {
            Type[] typeArguments = parameterizedType.getActualTypeArguments();
            this.requestType = (Class<TRequest>) typeArguments[0];
        } else {
            throw new IllegalStateException("<PERSON><PERSON> must specify generic type parameters");
        }
    }
    
    /**
     * Get the request type this handler supports (automatically detected)
     * @return the request class type
     */
    public final Class<TRequest> getRequestType() {
        return requestType;
    }
}
