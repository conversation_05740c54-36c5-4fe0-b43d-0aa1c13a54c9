package com.thanhnb.demo.handler.bus;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Spring Bus implementation for handling API requests
 * Routes requests to appropriate handlers based on request type
 */
@Component
@Slf4j
public class SpringBus {
    
    private final Map<Class<?>, ApiHandler<?, ?>> handlers = new HashMap<>();
    
    /**
     * Handle API request and return response
     */
    @SuppressWarnings("unchecked")
    public <TRequest, TResponse> TResponse handle(TRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }
        
        Class<?> requestType = request.getClass();
        ApiHandler<TRequest, TResponse> handler = (ApiHandler<TRequest, TResponse>) handlers.get(requestType);
        
        if (handler == null) {
            throw new IllegalArgumentException("No handler found for request type: " + requestType.getSimpleName());
        }
        
        log.debug("Handling API request of type: {}", requestType.getSimpleName());
        
        try {
            TResponse response = handler.handle(request);
            log.debug("Successfully handled API request of type: {}", requestType.getSimpleName());
            return response;
        } catch (Exception e) {
            log.error("Error handling API request of type: {}", requestType.getSimpleName(), e);
            throw e;
        }
    }
    
    /**
     * Register handler for specific request type
     */
    public <TRequest, TResponse> void registerHandler(Class<TRequest> requestType, ApiHandler<TRequest, TResponse> handler) {
        if (requestType == null || handler == null) {
            throw new IllegalArgumentException("Request type and handler cannot be null");
        }
        
        if (handlers.containsKey(requestType)) {
            log.warn("Overriding existing handler for request type: {}", requestType.getSimpleName());
        }
        
        handlers.put(requestType, handler);
        log.info("Registered API handler for request type: {}", requestType.getSimpleName());
    }
    
    /**
     * Check if handler exists for request type
     */
    public boolean hasHandler(Class<?> requestType) {
        return handlers.containsKey(requestType);
    }
    
    /**
     * Get all registered handlers
     */
    public Map<Class<?>, ApiHandler<?, ?>> getRegisteredHandlers() {
        return new HashMap<>(handlers);
    }
}
