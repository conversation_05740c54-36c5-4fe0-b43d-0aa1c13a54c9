package com.thanhnb.demo.handler.dto.response;

import com.thanhnb.demo.domain.model.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

/**
 * API response for paginated users
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UsersPageApiResponse {
    
    private Page<UserApiResponse> users;
    
    /**
     * Create from domain page
     */
    public static UsersPageApiResponse fromDomain(Page<User> userPage) {
        Page<UserApiResponse> userApiResponses = userPage.map(UserApiResponse::fromDomain);
        return new UsersPageApiResponse(userApiResponses);
    }
}
