package com.thanhnb.demo.handler.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API request for deleting a user
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeleteUserApiRequest {
    
    @NotNull(message = "User ID is required")
    private Long userId;
    
    public static DeleteUserApiRequest of(Long userId) {
        return new DeleteUserApiRequest(userId);
    }
}
