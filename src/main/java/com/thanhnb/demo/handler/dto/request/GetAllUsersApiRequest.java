package com.thanhnb.demo.handler.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Pageable;

/**
 * API request for getting all users with pagination
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetAllUsersApiRequest {
    
    private Pageable pageable;
    
    public static GetAllUsersApiRequest of(Pageable pageable) {
        return new GetAllUsersApiRequest(pageable);
    }
}
