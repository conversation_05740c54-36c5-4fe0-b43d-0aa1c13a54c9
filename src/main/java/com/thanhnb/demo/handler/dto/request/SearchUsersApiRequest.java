package com.thanhnb.demo.handler.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Pageable;

/**
 * API request for searching users
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchUsersApiRequest {
    
    private String searchTerm;
    private Pageable pageable;
    
    public static SearchUsersApiRequest of(String searchTerm, Pageable pageable) {
        return new SearchUsersApiRequest(searchTerm, pageable);
    }
}
