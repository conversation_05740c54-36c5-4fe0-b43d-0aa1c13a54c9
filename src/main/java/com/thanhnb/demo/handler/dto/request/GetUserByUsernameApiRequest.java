package com.thanhnb.demo.handler.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API request for getting user by username
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetUserByUsernameApiRequest {
    
    @NotBlank(message = "Username is required")
    private String username;
    
    public static GetUserByUsernameApiRequest of(String username) {
        return new GetUserByUsernameApiRequest(username);
    }
}
