package com.thanhnb.demo.handler.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API request for getting user by ID
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetUserByIdApiRequest {
    
    @NotNull(message = "User ID is required")
    private Long userId;
    
    public static GetUserByIdApiRequest of(Long userId) {
        return new GetUserByIdApiRequest(userId);
    }
}
