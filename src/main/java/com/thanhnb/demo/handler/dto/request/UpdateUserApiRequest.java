package com.thanhnb.demo.handler.dto.request;

import com.thanhnb.demo.domain.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API request for updating a user
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateUserApiRequest {
    
    @NotNull(message = "User ID is required")
    private Long userId;
    
    @Size(max = 100, message = "First name cannot exceed 100 characters")
    private String firstName;
    
    @Size(max = 100, message = "Last name cannot exceed 100 characters")
    private String lastName;
    
    @Size(max = 20, message = "Phone number cannot exceed 20 characters")
    private String phoneNumber;
    
    private User.Status status;
    
    public static UpdateUserApiRequest of(Long userId, String firstName, String lastName, String phoneNumber, User.Status status) {
        return new UpdateUserApiRequest(userId, firstName, lastName, phoneNumber, status);
    }
}
