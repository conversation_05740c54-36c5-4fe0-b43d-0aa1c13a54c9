package com.thanhnb.demo.handler.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API response for delete user operation
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeleteUserApiResponse {
    
    private Long deletedUserId;
    private String message;
    
    public static DeleteUserApiResponse of(Long deletedUserId, String message) {
        return new DeleteUserApiResponse(deletedUserId, message);
    }
    
    public static DeleteUserApiResponse success(Long deletedUserId) {
        return new DeleteUserApiResponse(deletedUserId, "User deleted successfully");
    }
}
