package com.thanhnb.demo.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * Custom exception class for API errors
 * Extends RuntimeException to provide custom error handling
 */
@Getter
@Setter
public class APIException extends RuntimeException {
    
    private String errorCode;
    private Object data;
    
    public APIException(String message) {
        super(message);
        this.errorCode = "API_ERROR";
    }
    
    public APIException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public APIException(String errorCode, String message, Object data) {
        super(message);
        this.errorCode = errorCode;
        this.data = data;
    }
    
    public APIException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public APIException(String errorCode, String message, Object data, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.data = data;
    }
} 