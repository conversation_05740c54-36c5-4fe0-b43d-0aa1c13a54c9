package com.thanhnb.demo.domain.model;

import java.time.LocalDateTime;

/**
 * Domain model for User
 * Contains business logic and domain rules
 */
public class User {
    
    public enum Status {
        ACTIVE, INACTIVE, SUSPENDED, DELETED
    }
    
    private final Long id;
    private final String username;
    private final String email;
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private Status status;
    private final LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Constructor for new user
    public User(String username, String email, String firstName, String lastName, String phoneNumber) {
        this.id = null; // Will be set by persistence layer
        this.username = validateUsername(username);
        this.email = validateEmail(email);
        this.firstName = firstName;
        this.lastName = lastName;
        this.phoneNumber = phoneNumber;
        this.status = Status.ACTIVE;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    // Constructor for existing user (from persistence)
    public User(Long id, String username, String email, String firstName, String lastName, 
                String phoneNumber, Status status, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.username = username;
        this.email = email;
        this.firstName = firstName;
        this.lastName = lastName;
        this.phoneNumber = phoneNumber;
        this.status = status;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    
    // Business methods
    public void updateProfile(String firstName, String lastName, String phoneNumber) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.phoneNumber = phoneNumber;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void activate() {
        if (this.status == Status.DELETED) {
            throw new IllegalStateException("Cannot activate deleted user");
        }
        this.status = Status.ACTIVE;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void deactivate() {
        if (this.status == Status.DELETED) {
            throw new IllegalStateException("Cannot deactivate deleted user");
        }
        this.status = Status.INACTIVE;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void suspend() {
        if (this.status == Status.DELETED) {
            throw new IllegalStateException("Cannot suspend deleted user");
        }
        this.status = Status.SUSPENDED;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void delete() {
        this.status = Status.DELETED;
        this.updatedAt = LocalDateTime.now();
    }
    
    public boolean isActive() {
        return this.status == Status.ACTIVE;
    }
    
    public boolean isDeleted() {
        return this.status == Status.DELETED;
    }
    
    // Validation methods
    private String validateUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("Username cannot be empty");
        }
        if (username.length() < 3 || username.length() > 50) {
            throw new IllegalArgumentException("Username must be between 3 and 50 characters");
        }
        return username.trim();
    }
    
    private String validateEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            throw new IllegalArgumentException("Email cannot be empty");
        }
        if (!email.matches("^[A-Za-z0-9+_.-]+@(.+)$")) {
            throw new IllegalArgumentException("Invalid email format");
        }
        return email.trim().toLowerCase();
    }
    
    // Getters
    public Long getId() { return id; }
    public String getUsername() { return username; }
    public String getEmail() { return email; }
    public String getFirstName() { return firstName; }
    public String getLastName() { return lastName; }
    public String getPhoneNumber() { return phoneNumber; }
    public Status getStatus() { return status; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    
    public String getFullName() {
        return (firstName != null ? firstName : "") + " " + (lastName != null ? lastName : "");
    }
}
