package com.thanhnb.demo.domain.port;

import com.thanhnb.demo.domain.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * Domain port for User repository
 * Defines the contract for user persistence operations
 */
public interface UserRepository {
    
    /**
     * Save a user
     */
    User save(User user);
    
    /**
     * Find user by ID
     */
    Optional<User> findById(Long id);
    
    /**
     * Find user by username
     */
    Optional<User> findByUsername(String username);
    
    /**
     * Find user by email
     */
    Optional<User> findByEmail(String email);
    
    /**
     * Find all users with pagination
     */
    Page<User> findAll(Pageable pageable);
    
    /**
     * Find users by status
     */
    Page<User> findByStatus(User.Status status, Pageable pageable);
    
    /**
     * Search users by term (username, email, name)
     */
    Page<User> searchUsers(String searchTerm, Pageable pageable);
    
    /**
     * Check if username exists
     */
    boolean existsByUsername(String username);
    
    /**
     * Check if email exists
     */
    boolean existsByEmail(String email);
    
    /**
     * Delete user by ID
     */
    void deleteById(Long id);
    
    /**
     * Count total users
     */
    long count();
    
    /**
     * Count users by status
     */
    long countByStatus(User.Status status);
}
