package com.thanhnb.demo.infrastructure.persistence.adapter;

import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.domain.port.UserRepository;
import com.thanhnb.demo.infrastructure.persistence.entity.UserEntity;
import com.thanhnb.demo.infrastructure.persistence.repository.JpaUserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Adapter that implements domain UserRepository using JPA
 * Converts between domain models and JPA entities
 */
@Component
@RequiredArgsConstructor
public class UserRepositoryAdapter implements UserRepository {
    
    private final JpaUserRepository jpaRepository;
    
    @Override
    public User save(User user) {
        UserEntity entity;
        
        if (user.getId() == null) {
            // New user
            entity = UserEntity.fromDomain(user);
        } else {
            // Existing user - update
            entity = jpaRepository.findById(user.getId())
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + user.getId()));
            entity.updateFromDomain(user);
        }
        
        UserEntity savedEntity = jpaRepository.save(entity);
        return savedEntity.toDomain();
    }
    
    @Override
    public Optional<User> findById(Long id) {
        return jpaRepository.findById(id)
            .map(UserEntity::toDomain);
    }
    
    @Override
    public Optional<User> findByUsername(String username) {
        return jpaRepository.findByUsername(username)
            .map(UserEntity::toDomain);
    }
    
    @Override
    public Optional<User> findByEmail(String email) {
        return jpaRepository.findByEmail(email)
            .map(UserEntity::toDomain);
    }
    
    @Override
    public Page<User> findAll(Pageable pageable) {
        return jpaRepository.findAll(pageable)
            .map(UserEntity::toDomain);
    }
    
    @Override
    public Page<User> findByStatus(User.Status status, Pageable pageable) {
        return jpaRepository.findByStatus(status, pageable)
            .map(UserEntity::toDomain);
    }
    
    @Override
    public Page<User> searchUsers(String searchTerm, Pageable pageable) {
        return jpaRepository.searchUsers(searchTerm, pageable)
            .map(UserEntity::toDomain);
    }
    
    @Override
    public boolean existsByUsername(String username) {
        return jpaRepository.existsByUsername(username);
    }
    
    @Override
    public boolean existsByEmail(String email) {
        return jpaRepository.existsByEmail(email);
    }
    
    @Override
    public void deleteById(Long id) {
        jpaRepository.deleteById(id);
    }
    
    @Override
    public long count() {
        return jpaRepository.count();
    }
    
    @Override
    public long countByStatus(User.Status status) {
        return jpaRepository.countByStatus(status);
    }
}
