package com.thanhnb.demo.infrastructure.persistence.entity;

import com.thanhnb.demo.common.entity.BaseEntity;
import com.thanhnb.demo.domain.model.User;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * JPA Entity for User
 * Maps domain model to database table
 */
@Entity
@Table(name = "users")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserEntity extends BaseEntity {
    
    @Column(unique = true, nullable = false, length = 50)
    private String username;
    
    @Column(unique = true, nullable = false)
    private String email;
    
    @Column(name = "first_name", length = 100)
    private String firstName;
    
    @Column(name = "last_name", length = 100)
    private String lastName;
    
    @Column(name = "phone_number", length = 20)
    private String phoneNumber;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private User.Status status;
    
    @Column(name = "keycloak_user_id")
    private String keycloakUserId;
    
    /**
     * Convert to domain model
     */
    public User toDomain() {
        return new User(
            this.getId(),
            this.username,
            this.email,
            this.firstName,
            this.lastName,
            this.phoneNumber,
            this.status,
            this.getCreatedAt(),
            this.getUpdatedAt()
        );
    }
    
    /**
     * Create from domain model
     */
    public static UserEntity fromDomain(User user) {
        UserEntity entity = UserEntity.builder()
            .username(user.getUsername())
            .email(user.getEmail())
            .firstName(user.getFirstName())
            .lastName(user.getLastName())
            .phoneNumber(user.getPhoneNumber())
            .status(user.getStatus())
            .build();

        // Set base entity fields
        entity.setId(user.getId());
        entity.setCreatedAt(user.getCreatedAt());
        entity.setUpdatedAt(user.getUpdatedAt());

        return entity;
    }
    
    /**
     * Update from domain model (for existing entities)
     */
    public void updateFromDomain(User user) {
        this.firstName = user.getFirstName();
        this.lastName = user.getLastName();
        this.phoneNumber = user.getPhoneNumber();
        this.status = user.getStatus();
        this.setUpdatedAt(user.getUpdatedAt());
    }
}
