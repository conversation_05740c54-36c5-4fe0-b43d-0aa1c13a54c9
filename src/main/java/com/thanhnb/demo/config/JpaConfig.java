package com.thanhnb.demo.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * JPA configuration
 */
@Configuration
@EnableJpaAuditing
@EnableJpaRepositories(basePackages = "com.thanhnb.demo.infrastructure.persistence.repository")
@EntityScan(basePackages = "com.thanhnb.demo.infrastructure.persistence.entity")
public class JpaConfig {
}