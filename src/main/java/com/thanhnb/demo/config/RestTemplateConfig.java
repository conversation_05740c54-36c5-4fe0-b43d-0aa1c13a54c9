package com.thanhnb.demo.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * Configuration for RestTemplate beans
 */
@Configuration
public class RestTemplateConfig {

    @Value("${app.rest-template.connection-timeout:5000}")
    private int connectionTimeout;

    @Value("${app.rest-template.read-timeout:10000}")
    private int readTimeout;

    /**
     * RestTemplate bean for HTTP requests
     */
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        
        // Add error handler and interceptors if needed
        // restTemplate.setErrorHandler(new CustomResponseErrorHandler());
        // restTemplate.getInterceptors().add(new LoggingRequestInterceptor());
        
        return restTemplate;
    }
} 