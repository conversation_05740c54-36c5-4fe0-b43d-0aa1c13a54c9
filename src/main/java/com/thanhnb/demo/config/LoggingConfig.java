package com.thanhnb.demo.config;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.AbstractRequestLoggingFilter;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.UUID;

/**
 * Configuration for HTTP request/response logging
 */
@Configuration
@Slf4j
public class LoggingConfig implements WebMvcConfigurer {

    /**
     * Custom request logging filter
     */
    @Bean
    public RequestLoggingFilter requestLoggingFilter() {
        RequestLoggingFilter filter = new RequestLoggingFilter();
        filter.setIncludeQueryString(true);
        filter.setIncludePayload(true);
        filter.setMaxPayloadLength(1000);
        filter.setIncludeHeaders(true);
        filter.setAfterMessagePrefix("REQUEST DATA: ");
        return filter;
    }

    /**
     * Request interceptor for MDC setup
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new RequestInterceptor());
    }

    /**
     * Custom request logging filter implementation
     */
    public static class RequestLoggingFilter extends AbstractRequestLoggingFilter {

        @Override
        protected void beforeRequest(HttpServletRequest request, String message) {
            // Setup MDC before logging
            setupMDC(request);
            log.info("INCOMING REQUEST: {} {} - {}", 
                    request.getMethod(), 
                    request.getRequestURI(),
                    message);
        }

        @Override
        protected void afterRequest(HttpServletRequest request, String message) {
            log.info("REQUEST COMPLETED: {} {} - {}", 
                    request.getMethod(), 
                    request.getRequestURI(),
                    message);
            // Clear MDC after request
            clearMDC();
        }

        private void setupMDC(HttpServletRequest request) {
            // Generate or get request ID
            String requestId = request.getHeader("X-Request-ID");
            if (requestId == null) {
                requestId = UUID.randomUUID().toString();
            }
            MDC.put("requestId", requestId);

            // Get username from security context
            try {
                Authentication auth = SecurityContextHolder.getContext().getAuthentication();
                if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
                    String username = auth.getName();
                    MDC.put("username", username);
                } else {
                    MDC.put("username", "anonymous");
                }
            } catch (Exception e) {
                MDC.put("username", "unknown");
            }
        }

        private void clearMDC() {
            MDC.remove("requestId");
            MDC.remove("username");
        }
    }

    /**
     * Request interceptor for additional processing
     */
    public static class RequestInterceptor implements HandlerInterceptor {

        @Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
            // Setup request ID header in response
            String requestId = MDC.get("requestId");
            if (requestId != null) {
                response.setHeader("X-Request-ID", requestId);
            }

            log.info("Processing request: {} {} by user: {}", 
                    request.getMethod(), 
                    request.getRequestURI(),
                    MDC.get("username"));
            
            return true;
        }

        @Override
        public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                                   Object handler, Exception ex) {
            log.info("Request completed: {} {} - Status: {} by user: {}", 
                    request.getMethod(), 
                    request.getRequestURI(),
                    response.getStatus(),
                    MDC.get("username"));

            if (ex != null) {
                log.error("Request failed with exception: {} {} by user: {}", 
                        request.getMethod(), 
                        request.getRequestURI(),
                        MDC.get("username"), ex);
            }
        }
    }
} 