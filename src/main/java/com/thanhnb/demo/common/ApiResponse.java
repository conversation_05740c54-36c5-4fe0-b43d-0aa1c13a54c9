package com.thanhnb.demo.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Standard API response format
 * Contains code, message and data fields
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    
    private String code;
    private String message;
    private T data;
    private LocalDateTime timestamp;
    
    // Success responses
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
                .code("SUCCESS")
                .message("Request processed successfully")
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    public static <T> ApiResponse<T> success(String message, T data) {
        return ApiResponse.<T>builder()
                .code("SUCCESS")
                .message(message)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    public static ApiResponse<Void> success() {
        return ApiResponse.<Void>builder()
                .code("SUCCESS")
                .message("Request processed successfully")
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    // Error responses
    public static <T> ApiResponse<T> error(String code, String message) {
        return ApiResponse.<T>builder()
                .code(code)
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    public static <T> ApiResponse<T> error(String code, String message, T data) {
        return ApiResponse.<T>builder()
                .code(code)
                .message(message)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    // Common error responses
    public static <T> ApiResponse<T> badRequest(String message) {
        return error("BAD_REQUEST", message);
    }
    
    public static <T> ApiResponse<T> notFound(String message) {
        return error("NOT_FOUND", message);
    }
    
    public static <T> ApiResponse<T> unauthorized(String message) {
        return error("UNAUTHORIZED", message);
    }
    
    public static <T> ApiResponse<T> forbidden(String message) {
        return error("FORBIDDEN", message);
    }
    
    public static <T> ApiResponse<T> internalServerError(String message) {
        return error("INTERNAL_SERVER_ERROR", message);
    }
} 