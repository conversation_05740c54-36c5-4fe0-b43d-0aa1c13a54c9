package com.thanhnb.demo.common.controller;

import com.thanhnb.demo.common.ApiResponse;
import com.thanhnb.demo.handler.bus.SpringBus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * Base controller class for all REST controllers
 * Provides common functionality and executeAPI method
 * Uses field injection to eliminate constructor boilerplate
 */
@Slf4j
public abstract class BaseController {

    @Autowired
    protected SpringBus springBus;
    
    /**
     * Execute API request through Spring Bus
     * @param request the API request object
     * @param <TRequest> request type
     * @param <TResponse> response type
     * @return ResponseEntity with API response
     */
    protected <TRequest, TResponse> ResponseEntity<ApiResponse<TResponse>> executeAPI(TRequest request) {
        return executeAPI(request, HttpStatus.OK);
    }
    
    /**
     * Execute API request through Spring Bus with custom HTTP status
     * @param request the API request object
     * @param successStatus HTTP status for successful response
     * @param <TRequest> request type
     * @param <TResponse> response type
     * @return ResponseEntity with API response
     */
    protected <TRequest, TResponse> ResponseEntity<ApiResponse<TResponse>> executeAPI(TRequest request, HttpStatus successStatus) {
        try {
            log.debug("Executing API request: {}", request.getClass().getSimpleName());
            
            TResponse response = springBus.handle(request);
            
            log.debug("API request executed successfully: {}", request.getClass().getSimpleName());
            
            return ResponseEntity.status(successStatus)
                    .body(ApiResponse.success("Request processed successfully", response));
                    
        } catch (IllegalArgumentException e) {
            log.warn("Bad request for API: {} - {}", request.getClass().getSimpleName(), e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest(e.getMessage()));

        } catch (Exception e) {
            log.error("Internal error processing API request: {}", request.getClass().getSimpleName(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.internalServerError("Internal server error: " + e.getMessage()));
        }
    }
    
    /**
     * Execute API request for operations that don't return data (like delete)
     * @param request the API request object
     * @param <TRequest> request type
     * @return ResponseEntity with success message
     */
    protected <TRequest> ResponseEntity<ApiResponse<Void>> executeVoidAPI(TRequest request) {
        return executeVoidAPI(request, "Operation completed successfully");
    }
    
    /**
     * Execute API request for operations that don't return data with custom message
     * @param request the API request object
     * @param successMessage custom success message
     * @param <TRequest> request type
     * @return ResponseEntity with success message
     */
    protected <TRequest> ResponseEntity<ApiResponse<Void>> executeVoidAPI(TRequest request, String successMessage) {
        try {
            log.debug("Executing void API request: {}", request.getClass().getSimpleName());
            
            springBus.handle(request);
            
            log.debug("Void API request executed successfully: {}", request.getClass().getSimpleName());
            
            return ResponseEntity.ok(ApiResponse.success(successMessage, null));
                    
        } catch (IllegalArgumentException e) {
            log.warn("Bad request for void API: {} - {}", request.getClass().getSimpleName(), e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest(e.getMessage()));

        } catch (Exception e) {
            log.error("Internal error processing void API request: {}", request.getClass().getSimpleName(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.internalServerError("Internal server error: " + e.getMessage()));
        }
    }
}
