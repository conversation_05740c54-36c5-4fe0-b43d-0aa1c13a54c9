package com.thanhnb.demo.client;

import com.thanhnb.demo.exception.APIException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Base client for calling both internal and external APIs
 * Provides unified interface for API communications
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BaseClient {

    private final RestTemplate restTemplate;

    @Value("${app.internal-api.base-url:http://localhost:8080}")
    private String internalApiBaseUrl;

    @Value("${app.internal-api.timeout:5000}")
    private int internalApiTimeout;

    @Value("${app.external-api.timeout:10000}")
    private int externalApiTimeout;

    @Value("${app.external-api.retry-attempts:3}")
    private int externalApiRetryAttempts;

    /**
     * Call Internal API
     */
    public <T> T callInternalAPI(String endpoint, HttpMethod method, Object requestBody, 
                                Class<T> responseType, Object... uriVariables) {
        return callInternalAPI(endpoint, method, requestBody, responseType, null, uriVariables);
    }

    /**
     * Call Internal API with custom headers
     */
    public <T> T callInternalAPI(String endpoint, HttpMethod method, Object requestBody, 
                                Class<T> responseType, Map<String, String> customHeaders, 
                                Object... uriVariables) {
        try {
            String fullUrl = internalApiBaseUrl + endpoint;
            log.info("Calling Internal API: {} {} - RequestId: {}", method, fullUrl, MDC.get("requestId"));

            HttpHeaders headers = createInternalApiHeaders(customHeaders);
            HttpEntity<?> requestEntity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<T> response = restTemplate.exchange(
                    fullUrl,
                    method,
                    requestEntity,
                    responseType,
                    uriVariables
            );

            log.info("Internal API call successful: {} {} - Status: {} - RequestId: {}", 
                    method, fullUrl, response.getStatusCode(), MDC.get("requestId"));
            return response.getBody();

        } catch (RestClientException e) {
            log.error("Error calling Internal API: {} {} - RequestId: {}", method, endpoint, MDC.get("requestId"), e);
            throw new APIException("INTERNAL_API_ERROR", "Failed to call internal API: " + endpoint, e);
        }
    }

    /**
     * Call Internal API with ParameterizedTypeReference
     */
    public <T> T callInternalAPI(String endpoint, HttpMethod method, Object requestBody, 
                                ParameterizedTypeReference<T> responseType, Map<String, String> customHeaders, 
                                Object... uriVariables) {
        try {
            String fullUrl = internalApiBaseUrl + endpoint;
            log.info("Calling Internal API: {} {} - RequestId: {}", method, fullUrl, MDC.get("requestId"));

            HttpHeaders headers = createInternalApiHeaders(customHeaders);
            HttpEntity<?> requestEntity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<T> response = restTemplate.exchange(
                    fullUrl,
                    method,
                    requestEntity,
                    responseType,
                    uriVariables
            );

            log.info("Internal API call successful: {} {} - Status: {} - RequestId: {}", 
                    method, fullUrl, response.getStatusCode(), MDC.get("requestId"));
            return response.getBody();

        } catch (RestClientException e) {
            log.error("Error calling Internal API: {} {} - RequestId: {}", method, endpoint, MDC.get("requestId"), e);
            throw new APIException("INTERNAL_API_ERROR", "Failed to call internal API: " + endpoint, e);
        }
    }

    /**
     * Call External API
     */
    public <T> T callExternalAPI(String url, HttpMethod method, Object requestBody, 
                                Class<T> responseType, Map<String, String> customHeaders) {
        return callExternalAPIWithRetry(() -> {
            log.info("Calling External API: {} {} - RequestId: {}", method, url, MDC.get("requestId"));

            HttpHeaders headers = createExternalApiHeaders(customHeaders);
            HttpEntity<?> requestEntity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<T> response = restTemplate.exchange(
                    url,
                    method,
                    requestEntity,
                    responseType
            );

            log.info("External API call successful: {} {} - Status: {} - RequestId: {}", 
                    method, url, response.getStatusCode(), MDC.get("requestId"));
            return response.getBody();
        }, url, method);
    }

    /**
     * Call External API with ParameterizedTypeReference
     */
    public <T> T callExternalAPI(String url, HttpMethod method, Object requestBody, 
                                ParameterizedTypeReference<T> responseType, Map<String, String> customHeaders) {
        return callExternalAPIWithRetry(() -> {
            log.info("Calling External API: {} {} - RequestId: {}", method, url, MDC.get("requestId"));

            HttpHeaders headers = createExternalApiHeaders(customHeaders);
            HttpEntity<?> requestEntity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<T> response = restTemplate.exchange(
                    url,
                    method,
                    requestEntity,
                    responseType
            );

            log.info("External API call successful: {} {} - Status: {} - RequestId: {}", 
                    method, url, response.getStatusCode(), MDC.get("requestId"));
            return response.getBody();
        }, url, method);
    }

    /**
     * Convenience methods for common HTTP operations
     */
    
    // Internal API convenience methods
    public <T> T getInternal(String endpoint, Class<T> responseType, Object... uriVariables) {
        return callInternalAPI(endpoint, HttpMethod.GET, null, responseType, uriVariables);
    }

    public <T> T postInternal(String endpoint, Object requestBody, Class<T> responseType) {
        return callInternalAPI(endpoint, HttpMethod.POST, requestBody, responseType);
    }

    public <T> T putInternal(String endpoint, Object requestBody, Class<T> responseType, Object... uriVariables) {
        return callInternalAPI(endpoint, HttpMethod.PUT, requestBody, responseType, uriVariables);
    }

    public void deleteInternal(String endpoint, Object... uriVariables) {
        callInternalAPI(endpoint, HttpMethod.DELETE, null, Void.class, uriVariables);
    }

    // External API convenience methods
    public <T> T getExternal(String url, Class<T> responseType, Map<String, String> headers) {
        return callExternalAPI(url, HttpMethod.GET, null, responseType, headers);
    }

    public <T> T postExternal(String url, Object requestBody, Class<T> responseType, Map<String, String> headers) {
        return callExternalAPI(url, HttpMethod.POST, requestBody, responseType, headers);
    }

    public <T> T putExternal(String url, Object requestBody, Class<T> responseType, Map<String, String> headers) {
        return callExternalAPI(url, HttpMethod.PUT, requestBody, responseType, headers);
    }

    public void deleteExternal(String url, Map<String, String> headers) {
        callExternalAPI(url, HttpMethod.DELETE, null, Void.class, headers);
    }

    /**
     * Create headers for internal API calls
     */
    private HttpHeaders createInternalApiHeaders(Map<String, String> customHeaders) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("User-Agent", "Internal-Service-Client/1.0");
        
        // Add request tracking
        String requestId = MDC.get("requestId");
        if (requestId != null) {
            headers.add("X-Request-ID", requestId);
        }
        
        String username = MDC.get("username");
        if (username != null) {
            headers.add("X-Username", username);
        }

        // Add custom headers
        if (customHeaders != null) {
            customHeaders.forEach(headers::add);
        }

        return headers;
    }

    /**
     * Create headers for external API calls
     */
    private HttpHeaders createExternalApiHeaders(Map<String, String> customHeaders) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("User-Agent", "External-Service-Client/1.0");
        
        // Add request tracking
        String requestId = MDC.get("requestId");
        if (requestId != null) {
            headers.add("X-Request-ID", requestId);
        }

        // Add custom headers
        if (customHeaders != null) {
            customHeaders.forEach(headers::add);
        }

        return headers;
    }

    /**
     * Call external API with retry mechanism
     */
    private <T> T callExternalAPIWithRetry(ExternalApiCall<T> apiCall, String url, HttpMethod method) {
        Exception lastException = null;

        for (int attempt = 1; attempt <= externalApiRetryAttempts; attempt++) {
            try {
                return apiCall.call();
            } catch (RestClientException e) {
                lastException = e;
                log.warn("External API call failed (attempt {}/{}): {} {} - RequestId: {} - Error: {}", 
                        attempt, externalApiRetryAttempts, method, url, MDC.get("requestId"), e.getMessage());

                if (attempt < externalApiRetryAttempts) {
                    try {
                        // Exponential backoff
                        long delayMs = (long) (Math.pow(2, attempt) * 1000);
                        Thread.sleep(Math.min(delayMs, 10000)); // Max 10 seconds
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        log.error("External API call failed after {} attempts: {} {} - RequestId: {}", 
                externalApiRetryAttempts, method, url, MDC.get("requestId"), lastException);
        throw new APIException("EXTERNAL_API_ERROR", 
                "Failed to call external API after " + externalApiRetryAttempts + " attempts: " + url, lastException);
    }

    /**
     * Functional interface for external API calls
     */
    @FunctionalInterface
    private interface ExternalApiCall<T> {
        T call() throws RestClientException;
    }

    /**
     * Health check methods
     */
    public boolean isInternalApiHealthy() {
        try {
            Map<String, String> headers = new HashMap<>();
            getInternal("/actuator/health", Map.class);
            return true;
        } catch (Exception e) {
            log.warn("Internal API health check failed - RequestId: {}", MDC.get("requestId"), e);
            return false;
        }
    }

    public boolean isExternalApiHealthy(String healthCheckUrl) {
        try {
            getExternal(healthCheckUrl, String.class, null);
            return true;
        } catch (Exception e) {
            log.warn("External API health check failed for: {} - RequestId: {}", healthCheckUrl, MDC.get("requestId"), e);
            return false;
        }
    }
} 