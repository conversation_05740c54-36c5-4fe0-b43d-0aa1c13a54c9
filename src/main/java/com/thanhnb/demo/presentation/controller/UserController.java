package com.thanhnb.demo.presentation.controller;

import com.thanhnb.demo.common.ApiResponse;
import com.thanhnb.demo.common.controller.BaseController;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.handler.bus.SpringBus;
import com.thanhnb.demo.handler.dto.request.*;
import com.thanhnb.demo.handler.dto.response.*;
import com.thanhnb.demo.presentation.dto.*;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST Controller for User operations
 * Uses BaseController and Spring Bus pattern
 */
@RestController
@RequestMapping("/api/v1/users")
@Slf4j
public class UserController extends BaseController {

    public UserController(SpringBus springBus) {
        super(springBus);
    }

    /**
     * Create a new user
     */
    @PostMapping
    public ResponseEntity<ApiResponse<UserResponse>> createUser(
            @Valid @RequestBody CreateUserRequest request) {
        
        log.info("REST request to create user: {}", request.username());
        
        User user = createUserUseCase.execute(request.toCommand());
        UserResponse response = UserResponse.fromDomain(user);
        
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("User created successfully", response));
    }

    /**
     * Update user information
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<UserResponse>> updateUser(
            @PathVariable Long id,
            @Valid @RequestBody UpdateUserRequest request) {
        
        log.info("REST request to update user: {}", id);
        
        User user = updateUserUseCase.execute(id, request.toCommand());
        UserResponse response = UserResponse.fromDomain(user);
        
        return ResponseEntity.ok(ApiResponse.success("User updated successfully", response));
    }

    /**
     * Get user by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<UserResponse>> getUserById(@PathVariable Long id) {
        
        log.info("REST request to get user: {}", id);
        
        User user = getUserUseCase.getById(id);
        UserResponse response = UserResponse.fromDomain(user);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * Get user by username
     */
    @GetMapping("/username/{username}")
    public ResponseEntity<ApiResponse<UserResponse>> getUserByUsername(@PathVariable String username) {
        
        log.info("REST request to get user by username: {}", username);
        
        User user = getUserUseCase.getByUsername(username);
        UserResponse response = UserResponse.fromDomain(user);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * Get all users with pagination
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getAllUsers(
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.info("REST request to get all users");
        
        Page<User> users = getUserUseCase.getAll(pageable);
        Page<UserResponse> response = users.map(UserResponse::fromDomain);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * Search users
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> searchUsers(
            @RequestParam String searchTerm,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.info("REST request to search users: {}", searchTerm);
        
        Page<User> users = getUserUseCase.search(searchTerm, pageable);
        Page<UserResponse> response = users.map(UserResponse::fromDomain);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * Get users by status
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getUsersByStatus(
            @PathVariable User.Status status,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.info("REST request to get users by status: {}", status);
        
        Page<User> users = getUserUseCase.getByStatus(status, pageable);
        Page<UserResponse> response = users.map(UserResponse::fromDomain);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * Delete user (soft delete)
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteUser(@PathVariable Long id) {
        
        log.info("REST request to delete user: {}", id);
        
        deleteUserUseCase.execute(id);
        
        return ResponseEntity.ok(ApiResponse.success("User deleted successfully", null));
    }

    /**
     * Activate user
     */
    @PatchMapping("/{id}/activate")
    public ResponseEntity<ApiResponse<UserResponse>> activateUser(@PathVariable Long id) {
        
        log.info("REST request to activate user: {}", id);
        
        UpdateUserRequest request = new UpdateUserRequest(null, null, null, User.Status.ACTIVE);
        User user = updateUserUseCase.execute(id, request.toCommand());
        UserResponse response = UserResponse.fromDomain(user);
        
        return ResponseEntity.ok(ApiResponse.success("User activated successfully", response));
    }

    /**
     * Deactivate user
     */
    @PatchMapping("/{id}/deactivate")
    public ResponseEntity<ApiResponse<UserResponse>> deactivateUser(@PathVariable Long id) {
        
        log.info("REST request to deactivate user: {}", id);
        
        UpdateUserRequest request = new UpdateUserRequest(null, null, null, User.Status.INACTIVE);
        User user = updateUserUseCase.execute(id, request.toCommand());
        UserResponse response = UserResponse.fromDomain(user);
        
        return ResponseEntity.ok(ApiResponse.success("User deactivated successfully", response));
    }

    /**
     * Check if username exists
     */
    @GetMapping("/check/username/{username}")
    public ResponseEntity<ApiResponse<Boolean>> checkUsernameExists(@PathVariable String username) {
        
        log.info("REST request to check username exists: {}", username);
        
        boolean exists = getUserUseCase.existsByUsername(username);
        
        return ResponseEntity.ok(ApiResponse.success(exists));
    }

    /**
     * Check if email exists
     */
    @GetMapping("/check/email")
    public ResponseEntity<ApiResponse<Boolean>> checkEmailExists(@RequestParam String email) {
        
        log.info("REST request to check email exists: {}", email);
        
        boolean exists = getUserUseCase.existsByEmail(email);
        
        return ResponseEntity.ok(ApiResponse.success(exists));
    }
}
