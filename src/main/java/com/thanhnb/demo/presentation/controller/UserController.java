package com.thanhnb.demo.presentation.controller;

import com.thanhnb.demo.common.ApiResponse;
import com.thanhnb.demo.common.controller.BaseController;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.handler.bus.SpringBus;
import com.thanhnb.demo.presentation.dto.*;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST Controller for User operations
 * Uses BaseController and Spring Bus pattern
 * No constructor needed - SpringBus is injected via BaseController
 */
@RestController
@RequestMapping("/api/v1/users")
@Slf4j
public class UserController extends BaseController {

    /**
     * Create a new user
     */
    @PostMapping
    public ResponseEntity<ApiResponse<UserResponse>> createUser(
            @Valid @RequestBody CreateUserRequest request) {

        log.info("REST request to create user: {}", request.username());

        return executeAPI(request, HttpStatus.CREATED);
    }

    /**
     * Update user information
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<UserResponse>> updateUser(
            @PathVariable Long id,
            @Valid @RequestBody UpdateUserRequest request) {

        log.info("REST request to update user: {}", id);

        UpdateUserWithIdRequest updateRequest = new UpdateUserWithIdRequest(id, request);

        return executeAPI(updateRequest);
    }

    /**
     * Get user by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<UserResponse>> getUserById(@PathVariable Long id) {

        log.info("REST request to get user: {}", id);

        GetUserByIdRequest request = GetUserByIdRequest.of(id);

        return executeAPI(request);
    }

    /**
     * Get user by username
     */
    @GetMapping("/username/{username}")
    public ResponseEntity<ApiResponse<UserApiResponse>> getUserByUsername(@PathVariable String username) {

        log.info("REST request to get user by username: {}", username);

        GetUserByUsernameApiRequest apiRequest = GetUserByUsernameApiRequest.of(username);

        return executeAPI(apiRequest);
    }

    /**
     * Get all users with pagination
     */
    @GetMapping
    public ResponseEntity<ApiResponse<UsersPageApiResponse>> getAllUsers(
            @PageableDefault(size = 20) Pageable pageable) {

        log.info("REST request to get all users");

        GetAllUsersApiRequest apiRequest = GetAllUsersApiRequest.of(pageable);

        return executeAPI(apiRequest);
    }

    /**
     * Search users
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<UsersPageApiResponse>> searchUsers(
            @RequestParam String searchTerm,
            @PageableDefault(size = 20) Pageable pageable) {

        log.info("REST request to search users: {}", searchTerm);

        SearchUsersApiRequest apiRequest = SearchUsersApiRequest.of(searchTerm, pageable);

        return executeAPI(apiRequest);
    }

    /**
     * Get users by status
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<ApiResponse<UsersPageApiResponse>> getUsersByStatus(
            @PathVariable User.Status status,
            @PageableDefault(size = 20) Pageable pageable) {

        log.info("REST request to get users by status: {}", status);

        // Create API request for getting users by status
        GetAllUsersApiRequest apiRequest = GetAllUsersApiRequest.of(pageable);

        return executeAPI(apiRequest);
    }

    /**
     * Delete user (soft delete)
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<DeleteUserApiResponse>> deleteUser(@PathVariable Long id) {

        log.info("REST request to delete user: {}", id);

        DeleteUserApiRequest apiRequest = DeleteUserApiRequest.of(id);

        return executeAPI(apiRequest);
    }

    /**
     * Activate user
     */
    @PatchMapping("/{id}/activate")
    public ResponseEntity<ApiResponse<UserApiResponse>> activateUser(@PathVariable Long id) {

        log.info("REST request to activate user: {}", id);

        UpdateUserApiRequest apiRequest = UpdateUserApiRequest.of(id, null, null, null, User.Status.ACTIVE);

        return executeAPI(apiRequest);
    }

    /**
     * Deactivate user
     */
    @PatchMapping("/{id}/deactivate")
    public ResponseEntity<ApiResponse<UserApiResponse>> deactivateUser(@PathVariable Long id) {

        log.info("REST request to deactivate user: {}", id);

        UpdateUserApiRequest apiRequest = UpdateUserApiRequest.of(id, null, null, null, User.Status.INACTIVE);

        return executeAPI(apiRequest);
    }
}
