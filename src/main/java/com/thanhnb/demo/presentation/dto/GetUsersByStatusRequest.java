package com.thanhnb.demo.presentation.dto;

import com.thanhnb.demo.domain.model.User;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.domain.Pageable;

/**
 * REST API request for getting users by status
 */
public record GetUsersByStatusRequest(
    @NotNull(message = "Status is required")
    User.Status status,
    
    Pageable pageable
) {
    
    public static GetUsersByStatusRequest of(User.Status status, Pageable pageable) {
        return new GetUsersByStatusRequest(status, pageable);
    }
}
