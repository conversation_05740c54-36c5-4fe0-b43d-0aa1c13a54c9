package com.thanhnb.demo.presentation.dto;

import com.thanhnb.demo.domain.model.User;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * REST API response for user information
 */
public record UserResponse(
    Long id,
    String username,
    String email,
    String firstName,
    String lastName,
    String fullName,
    String phoneNumber,
    User.Status status,
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    LocalDateTime createdAt,
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    LocalDateTime updatedAt
) {
    
    /**
     * Create from domain model
     */
    public static UserResponse fromDomain(User user) {
        return new UserResponse(
            user.getId(),
            user.getUsername(),
            user.getEmail(),
            user.getFirstName(),
            user.getLastName(),
            user.getFullName(),
            user.getPhoneNumber(),
            user.getStatus(),
            user.getCreatedAt(),
            user.getUpdatedAt()
        );
    }
}
