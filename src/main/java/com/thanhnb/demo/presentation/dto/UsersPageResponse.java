package com.thanhnb.demo.presentation.dto;

import com.thanhnb.demo.domain.model.User;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * REST API response for paginated users
 */
public record UsersPageResponse(
    List<UserResponse> users,
    int currentPage,
    int totalPages,
    long totalElements,
    int size,
    boolean hasNext,
    boolean hasPrevious
) {
    
    /**
     * Create from domain page
     */
    public static UsersPageResponse fromDomain(Page<User> userPage) {
        List<UserResponse> users = userPage.getContent()
            .stream()
            .map(UserResponse::fromDomain)
            .toList();
            
        return new UsersPageResponse(
            users,
            userPage.getNumber(),
            userPage.getTotalPages(),
            userPage.getTotalElements(),
            userPage.getSize(),
            userPage.hasNext(),
            userPage.hasPrevious()
        );
    }
}
