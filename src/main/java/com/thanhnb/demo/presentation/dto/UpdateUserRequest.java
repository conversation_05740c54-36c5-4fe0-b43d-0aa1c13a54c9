package com.thanhnb.demo.presentation.dto;

import com.thanhnb.demo.application.dto.UpdateUserCommand;
import com.thanhnb.demo.domain.model.User;
import jakarta.validation.constraints.Size;

/**
 * REST API request for updating a user
 */
public record UpdateUserRequest(
    
    @Size(max = 100, message = "First name cannot exceed 100 characters")
    String firstName,
    
    @Size(max = 100, message = "Last name cannot exceed 100 characters")
    String lastName,
    
    @Size(max = 20, message = "Phone number cannot exceed 20 characters")
    String phoneNumber,
    
    User.Status status
) {
    
    /**
     * Convert to application command
     */
    public UpdateUserCommand toCommand() {
        return UpdateUserCommand.of(firstName, lastName, phoneNumber, status);
    }
}
