package com.thanhnb.demo.presentation.dto;

import com.thanhnb.demo.application.dto.UpdateUserCommand;
import com.thanhnb.demo.domain.model.User;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * REST API request for updating a user
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateUserRequest {

    @Size(max = 100, message = "First name cannot exceed 100 characters")
    private String firstName;

    @Size(max = 100, message = "Last name cannot exceed 100 characters")
    private String lastName;

    @Size(max = 20, message = "Phone number cannot exceed 20 characters")
    private String phoneNumber;

    private User.Status status;

    /**
     * Convert to application command
     */
    public UpdateUserCommand toCommand() {
        return UpdateUserCommand.of(firstName, lastName, phoneNumber, status);
    }
}
