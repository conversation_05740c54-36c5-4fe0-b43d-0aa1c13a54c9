package com.thanhnb.demo.presentation.dto;

import jakarta.validation.constraints.NotBlank;
import org.springframework.data.domain.Pageable;

/**
 * REST API request for searching users
 */
public record SearchUsersRequest(
    @NotBlank(message = "Search term is required")
    String searchTerm,
    
    Pageable pageable
) {
    
    public static SearchUsersRequest of(String searchTerm, Pageable pageable) {
        return new SearchUsersRequest(searchTerm, pageable);
    }
}
