package com.thanhnb.demo.presentation.dto;

import com.thanhnb.demo.application.dto.UpdateUserCommand;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * REST API request for updating a user with ID
 * Combines path variable ID with request body
 */
public record UpdateUserWithIdRequest(
    @NotNull(message = "User ID is required")
    Long userId,
    
    @Valid
    @NotNull(message = "Update request is required")
    UpdateUserRequest updateRequest
) {
    
    /**
     * Convert to application command
     */
    public UpdateUserCommand toCommand() {
        return updateRequest.toCommand();
    }
    
    public static UpdateUserWithIdRequest of(Long userId, UpdateUserRequest updateRequest) {
        return new UpdateUserWithIdRequest(userId, updateRequest);
    }
}
