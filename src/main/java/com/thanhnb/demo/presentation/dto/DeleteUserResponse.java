package com.thanhnb.demo.presentation.dto;

/**
 * REST API response for delete user operation
 */
public record DeleteUserResponse(
    Long userId,
    String message
) {
    
    public static DeleteUserResponse of(Long userId, String message) {
        return new DeleteUserResponse(userId, message);
    }
    
    public static DeleteUserResponse success(Long userId) {
        return new DeleteUserResponse(userId, "User deleted successfully");
    }
}
