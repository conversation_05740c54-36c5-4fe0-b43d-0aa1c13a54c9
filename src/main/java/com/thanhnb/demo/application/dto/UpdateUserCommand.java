package com.thanhnb.demo.application.dto;

import com.thanhnb.demo.domain.model.User;
import jakarta.validation.constraints.Size;

/**
 * Command for updating a user
 */
public record UpdateUserCommand(
    
    @Size(max = 100, message = "First name cannot exceed 100 characters")
    String firstName,
    
    @Size(max = 100, message = "Last name cannot exceed 100 characters")
    String lastName,
    
    @Size(max = 20, message = "Phone number cannot exceed 20 characters")
    String phoneNumber,
    
    User.Status status
) {
    
    public static UpdateUserCommand of(String firstName, String lastName, String phoneNumber, User.Status status) {
        return new UpdateUserCommand(firstName, lastName, phoneNumber, status);
    }
}
