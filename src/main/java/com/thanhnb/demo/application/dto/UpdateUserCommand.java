package com.thanhnb.demo.application.dto;

import com.thanhnb.demo.domain.model.User;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Command for updating a user
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateUserCommand {

    @Size(max = 100, message = "First name cannot exceed 100 characters")
    private String firstName;

    @Size(max = 100, message = "Last name cannot exceed 100 characters")
    private String lastName;

    @Size(max = 20, message = "Phone number cannot exceed 20 characters")
    private String phoneNumber;

    private User.Status status;

    public static UpdateUserCommand of(String firstName, String lastName, String phoneNumber, User.Status status) {
        return new UpdateUserCommand(firstName, lastName, phoneNumber, status);
    }
}
