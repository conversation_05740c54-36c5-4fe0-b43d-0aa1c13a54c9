package com.thanhnb.demo.application.usecase;

import com.thanhnb.demo.application.dto.CreateUserCommand;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.domain.port.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Use case for creating a new user
 * Contains business logic for user creation
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CreateUserUseCase {
    
    private final UserRepository userRepository;
    
    @Transactional
    public User execute(CreateUserCommand command) {
        log.info("Creating user with username: {}", command.username());
        
        // Business rules validation
        validateUserDoesNotExist(command.username(), command.email());
        
        // Create domain model
        User user = new User(
            command.username(),
            command.email(),
            command.firstName(),
            command.lastName(),
            command.phoneNumber()
        );
        
        // Save user
        User savedUser = userRepository.save(user);
        
        log.info("User created successfully with ID: {}", savedUser.getId());
        return savedUser;
    }
    
    private void validateUserDoesNotExist(String username, String email) {
        if (userRepository.existsByUsername(username)) {
            throw new IllegalArgumentException("Username already exists: " + username);
        }
        
        if (userRepository.existsByEmail(email)) {
            throw new IllegalArgumentException("Email already exists: " + email);
        }
    }
}
