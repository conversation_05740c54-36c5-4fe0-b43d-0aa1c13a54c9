package com.thanhnb.demo.application.usecase;

import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.domain.port.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Use case for deleting users
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeleteUserUseCase {
    
    private final UserRepository userRepository;
    
    @Transactional
    public void execute(Long userId) {
        log.info("Deleting user with ID: {}", userId);
        
        // Find user
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
        
        // Soft delete - mark as deleted
        user.delete();
        userRepository.save(user);
        
        log.info("User soft deleted successfully: {}", user.getUsername());
    }
    
    @Transactional
    public void hardDelete(Long userId) {
        log.warn("Hard deleting user with ID: {}", userId);
        
        // Verify user exists
        if (!userRepository.findById(userId).isPresent()) {
            throw new IllegalArgumentException("User not found with ID: " + userId);
        }
        
        // Hard delete from database
        userRepository.deleteById(userId);
        
        log.warn("User hard deleted successfully with ID: {}", userId);
    }
}
