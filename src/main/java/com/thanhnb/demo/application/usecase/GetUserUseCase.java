package com.thanhnb.demo.application.usecase;

import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.domain.port.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Use case for retrieving users
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GetUserUseCase {
    
    private final UserRepository userRepository;
    
    @Transactional(readOnly = true)
    public User getById(Long userId) {
        log.debug("Getting user by ID: {}", userId);
        
        return userRepository.findById(userId)
            .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
    }
    
    @Transactional(readOnly = true)
    public User getByUsername(String username) {
        log.debug("Getting user by username: {}", username);
        
        return userRepository.findByUsername(username)
            .orElseThrow(() -> new IllegalArgumentException("User not found with username: " + username));
    }
    
    @Transactional(readOnly = true)
    public Page<User> getAll(Pageable pageable) {
        log.debug("Getting all users with pagination: {}", pageable);
        
        return userRepository.findAll(pageable);
    }
    
    @Transactional(readOnly = true)
    public Page<User> getByStatus(User.Status status, Pageable pageable) {
        log.debug("Getting users by status: {} with pagination: {}", status, pageable);
        
        return userRepository.findByStatus(status, pageable);
    }
    
    @Transactional(readOnly = true)
    public Page<User> search(String searchTerm, Pageable pageable) {
        log.debug("Searching users with term: {} and pagination: {}", searchTerm, pageable);
        
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return getAll(pageable);
        }
        
        return userRepository.searchUsers(searchTerm.trim(), pageable);
    }
    
    @Transactional(readOnly = true)
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }
    
    @Transactional(readOnly = true)
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }
}
