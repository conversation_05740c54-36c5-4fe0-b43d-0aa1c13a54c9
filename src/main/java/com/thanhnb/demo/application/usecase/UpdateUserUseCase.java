package com.thanhnb.demo.application.usecase;

import com.thanhnb.demo.application.dto.UpdateUserCommand;
import com.thanhnb.demo.domain.model.User;
import com.thanhnb.demo.domain.port.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Use case for updating user information
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UpdateUserUseCase {
    
    private final UserRepository userRepository;
    
    @Transactional
    public User execute(Long userId, UpdateUserCommand command) {
        log.info("Updating user with ID: {}", userId);
        
        // Find user
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
        
        // Update profile information
        if (hasProfileChanges(command)) {
            user.updateProfile(command.firstName(), command.lastName(), command.phoneNumber());
        }
        
        // Update status if provided
        if (command.status() != null && command.status() != user.getStatus()) {
            updateUserStatus(user, command.status());
        }
        
        // Save updated user
        User updatedUser = userRepository.save(user);
        
        log.info("User updated successfully: {}", updatedUser.getUsername());
        return updatedUser;
    }
    
    private boolean hasProfileChanges(UpdateUserCommand command) {
        return command.firstName() != null || 
               command.lastName() != null || 
               command.phoneNumber() != null;
    }
    
    private void updateUserStatus(User user, User.Status newStatus) {
        switch (newStatus) {
            case ACTIVE -> user.activate();
            case INACTIVE -> user.deactivate();
            case SUSPENDED -> user.suspend();
            case DELETED -> user.delete();
        }
    }
}
