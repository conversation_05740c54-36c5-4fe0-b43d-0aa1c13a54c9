# Demo Spring Boot Application - Clean Architecture

## 🎯 **Mô tả**
Ứng dụng Spring Boot demo với Clean Architecture đã được **refactor và cải thiện**, sử dụng Spring Bus pattern cho handler management. Đ<PERSON> fix các vấn đề critical và major để đạt code quality cao nhất.

## 🏗️ **Các cải thiện đã thực hiện:**

### ✅ **CRITICAL FIXES:**
1. **Fix HandlerConfiguration bug** - Sử dụng reflection thay vì getRequestType() method
2. **Fix Entity auditing** - UserEntity extends BaseEntity với @EntityListeners
3. **Consolidate DTO layers** - Loại bỏ duplicate handler DTOs, chỉ dùng presentation DTOs
4. **Convert record to class** - Sử dụng class với @Data thay vì record

### ✅ **MAJOR IMPROVEMENTS:**
1. **Security configuration linh hoạt** - <PERSON><PERSON> thể cấu hình từ application.yml
2. **CORS configuration** - Configurable từ application.yml
3. **Development profile** - Security disabled cho development
4. **BaseApiHandler pattern** - Tự động detect request type bằng reflection

## Cấu trúc Package

```
com.thanhnb.demo/
├── controller/         # REST Controllers
├── entity/            # JPA Entities  
├── dto/               # Data Transfer Objects
│   ├── request/       # Request DTOs
│   └── response/      # Response DTOs
├── service/           # Business Logic Services
│   └── impl/          # Service Implementations
├── handler/           # Event Handlers & Processing Logic
├── repository/        # Data Access Layer
├── client/            # External/Internal API Clients
├── exception/         # Custom Exceptions
├── config/            # Configuration Classes
└── common/            # Common Utilities & Response Format
```

## Tính năng chính

- **Clean Architecture**: Tách biệt rõ ràng các layer
- **Standard API Response**: Format response chung với code, message, data
- **Exception Handling**: Global exception handler với custom APIException
- **Event-Driven**: Sử dụng Spring Events cho loose coupling
- **Security**: Tích hợp Keycloak cho authentication/authorization
- **API Clients**: RestTemplate cho internal/external API calls
- **Auditing**: JPA auditing cho created/updated timestamps

## Requirements

- Java 17+
- Gradle 7+
- PostgreSQL 12+ (hoặc H2 cho development)
- Keycloak 23+ (optional cho development)

## 🚀 **Quick Start**

### **1. Development Mode (Recommended)**
```bash
# Clone repository
git clone <repository-url>
cd demo

# Run with development profile (H2 database, security disabled)
export JAVA_HOME=/path/to/java17
./gradlew bootRun --args='--spring.profiles.active=dev'
```

### **2. Production Mode**
```bash
# Setup PostgreSQL
createdb demo_db
createuser demo_user

# Run with production profile
./gradlew bootRun --args='--spring.profiles.active=prod'
```

3. **Cấu hình Keycloak** (Optional)
```bash
# Download và chạy Keycloak
# Tạo realm: demo-realm
# Tạo client: demo-client
```

4. **Cập nhật application.properties**
```properties
# Database
spring.datasource.url=****************************************
spring.datasource.username=demo_user
spring.datasource.password=demo_password

# Keycloak
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8080/realms/demo-realm/protocol/openid_connect/certs
```

## Chạy ứng dụng

```bash
# Development với H2
./gradlew bootRun -Pprofiles=dev

# Production với PostgreSQL
./gradlew bootRun
```

## API Endpoints

### User Management
- `GET /api/v1/users` - Lấy danh sách users
- `POST /api/v1/users` - Tạo user mới
- `GET /api/v1/users/{id}` - Lấy user theo ID
- `PUT /api/v1/users/{id}` - Cập nhật user
- `DELETE /api/v1/users/{id}` - Xóa user (soft delete)
- `PATCH /api/v1/users/{id}/activate` - Kích hoạt user
- `PATCH /api/v1/users/{id}/deactivate` - Vô hiệu hóa user

### Health Check
- `GET /actuator/health` - Health check

## Sử dụng API Clients

### Internal API Client
```java
@Autowired
private InternalApiClient internalApiClient;

// GET request
UserResponse user = internalApiClient.callInternalApiGet(
    "/api/users/{id}", 
    UserResponse.class, 
    userId
);

// POST request  
UserResponse created = internalApiClient.callInternalApiPost(
    "/api/users",
    createRequest,
    UserResponse.class
);
```

### External API Client
```java
@Autowired
private ExternalApiClient externalApiClient;

// With authentication
ExternalData data = externalApiClient.callExternalApiWithAuth(
    "https://api.external.com/data",
    "bearer-token",
    ExternalData.class
);

// With API key
ExternalData data = externalApiClient.callExternalApiWithApiKey(
    "https://api.external.com/data",
    "api-key-value",
    "X-API-Key",
    ExternalData.class
);
```

## Event Handling

Ứng dụng sử dụng Spring Events để xử lý các side effects:

```java
// Events được publish tự động từ UserHandler
// - UserCreatedEvent
// - UserUpdatedEvent  
// - UserDeletedEvent
// - UserStatusChangedEvent

// Customize UserEventListener để thêm logic xử lý
@EventListener
@Async
public void handleUserCreated(UserHandler.UserCreatedEvent event) {
    // Send welcome email
    // Sync with external systems
    // Audit logging
}
```

## Security & Authorization

```java
// Role-based access control
@PreAuthorize("hasRole('ADMIN')")
@PreAuthorize("hasRole('USER_MANAGER')")

// User-specific access
@PreAuthorize("@userHandler.handleGetUserById(#id).username == authentication.name")
```

## Testing

```bash
# Run all tests
./gradlew test

# Run specific test
./gradlew test --tests UserServiceTest
```

## Production Deployment

1. **Build JAR**
```bash
./gradlew build
```

2. **Run với production profile**
```bash
java -jar build/libs/demo-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod
```

3. **Docker** (optional)
```dockerfile
FROM openjdk:17-jre-slim
COPY build/libs/demo-0.0.1-SNAPSHOT.jar app.jar
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## Monitoring

- Health checks: `/actuator/health`
- Metrics: `/actuator/metrics`
- Application logs với structured logging

## Best Practices

- Sử dụng DTOs cho input/output
- Validation ở controller layer
- Business logic ở service layer
- Event-driven cho side effects
- Consistent error handling
- Structured logging
- API versioning (/api/v1/)

## 📝 **Mẫu sử dụng - Spring Bus Pattern**

### **1. Tạo Controller mới:**
```java
@RestController
@RequestMapping("/api/v1/products")
@Slf4j
public class ProductController extends BaseController {

    @PostMapping
    public ResponseEntity<ApiResponse<ProductResponse>> createProduct(
            @Valid @RequestBody CreateProductRequest request) {

        log.info("REST request to create product: {}", request.getName());
        return executeAPI(request, HttpStatus.CREATED);
    }
}
```

### **2. Tạo Handler tương ứng:**
```java
@Component
@RequiredArgsConstructor
@Slf4j
public class CreateProductHandler extends BaseApiHandler<CreateProductRequest, ProductResponse> {

    private final CreateProductUseCase createProductUseCase;

    @Override
    public ProductResponse handle(CreateProductRequest request) {
        log.info("Handling create product request: {}", request.getName());

        Product product = createProductUseCase.execute(request.toCommand());
        return ProductResponse.fromDomain(product);
    }
}
```

### **3. Test API:**
```bash
curl -X POST http://localhost:8080/api/v1/users \
  -H "Content-Type: application/json" \
  -d '{
    "username": "john_doe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "phoneNumber": "+1234567890"
  }'
```

## 🎯 **Key Benefits của kiến trúc này:**

✅ **Clean Architecture** - Tách biệt rõ ràng các layer
✅ **Spring Bus Pattern** - Loose coupling, easy testing
✅ **Flexible Security** - Configurable từ application.yml
✅ **Auto Handler Registration** - Sử dụng reflection
✅ **Development Friendly** - H2 database, security disabled
✅ **Production Ready** - PostgreSQL, Keycloak integration

## Contribution

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request