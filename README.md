# Demo Enterprise Java Application

## Mô tả
Đây là một ứng dụng Java enterprise được xây dựng với kiến trúc clean, d<PERSON> đọc, d<PERSON> bảo trì và sẵn sàng cho enterprise. Ứng dụng sử dụng Spring Boot, Spring Security với Keycloak, và PostgreSQL.

## Cấu trúc Package

```
com.thanhnb.demo/
├── controller/         # REST Controllers
├── entity/            # JPA Entities  
├── dto/               # Data Transfer Objects
│   ├── request/       # Request DTOs
│   └── response/      # Response DTOs
├── service/           # Business Logic Services
│   └── impl/          # Service Implementations
├── handler/           # Event Handlers & Processing Logic
├── repository/        # Data Access Layer
├── client/            # External/Internal API Clients
├── exception/         # Custom Exceptions
├── config/            # Configuration Classes
└── common/            # Common Utilities & Response Format
```

## Tính năng chính

- **Clean Architecture**: Tách biệt rõ ràng các layer
- **Standard API Response**: Format response chung với code, message, data
- **Exception Handling**: Global exception handler với custom APIException
- **Event-Driven**: Sử dụng Spring Events cho loose coupling
- **Security**: <PERSON><PERSON><PERSON> hợp Keycloak cho authentication/authorization
- **API Clients**: RestTemplate cho internal/external API calls
- **Auditing**: JPA auditing cho created/updated timestamps

## Requirements

- Java 17+
- Gradle 7+
- PostgreSQL 12+ (hoặc H2 cho development)
- Keycloak 23+ (optional cho development)

## Cài đặt

1. **Clone repository**
```bash
git clone <repository-url>
cd demo
```

2. **Cấu hình database**
```bash
# PostgreSQL
createdb demo_db
createuser demo_user
```

3. **Cấu hình Keycloak** (Optional)
```bash
# Download và chạy Keycloak
# Tạo realm: demo-realm
# Tạo client: demo-client
```

4. **Cập nhật application.properties**
```properties
# Database
spring.datasource.url=****************************************
spring.datasource.username=demo_user
spring.datasource.password=demo_password

# Keycloak
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8080/realms/demo-realm/protocol/openid_connect/certs
```

## Chạy ứng dụng

```bash
# Development với H2
./gradlew bootRun -Pprofiles=dev

# Production với PostgreSQL
./gradlew bootRun
```

## API Endpoints

### User Management
- `GET /api/v1/users` - Lấy danh sách users
- `POST /api/v1/users` - Tạo user mới
- `GET /api/v1/users/{id}` - Lấy user theo ID
- `PUT /api/v1/users/{id}` - Cập nhật user
- `DELETE /api/v1/users/{id}` - Xóa user (soft delete)
- `PATCH /api/v1/users/{id}/activate` - Kích hoạt user
- `PATCH /api/v1/users/{id}/deactivate` - Vô hiệu hóa user

### Health Check
- `GET /actuator/health` - Health check

## Sử dụng API Clients

### Internal API Client
```java
@Autowired
private InternalApiClient internalApiClient;

// GET request
UserResponse user = internalApiClient.callInternalApiGet(
    "/api/users/{id}", 
    UserResponse.class, 
    userId
);

// POST request  
UserResponse created = internalApiClient.callInternalApiPost(
    "/api/users",
    createRequest,
    UserResponse.class
);
```

### External API Client
```java
@Autowired
private ExternalApiClient externalApiClient;

// With authentication
ExternalData data = externalApiClient.callExternalApiWithAuth(
    "https://api.external.com/data",
    "bearer-token",
    ExternalData.class
);

// With API key
ExternalData data = externalApiClient.callExternalApiWithApiKey(
    "https://api.external.com/data",
    "api-key-value",
    "X-API-Key",
    ExternalData.class
);
```

## Event Handling

Ứng dụng sử dụng Spring Events để xử lý các side effects:

```java
// Events được publish tự động từ UserHandler
// - UserCreatedEvent
// - UserUpdatedEvent  
// - UserDeletedEvent
// - UserStatusChangedEvent

// Customize UserEventListener để thêm logic xử lý
@EventListener
@Async
public void handleUserCreated(UserHandler.UserCreatedEvent event) {
    // Send welcome email
    // Sync with external systems
    // Audit logging
}
```

## Security & Authorization

```java
// Role-based access control
@PreAuthorize("hasRole('ADMIN')")
@PreAuthorize("hasRole('USER_MANAGER')")

// User-specific access
@PreAuthorize("@userHandler.handleGetUserById(#id).username == authentication.name")
```

## Testing

```bash
# Run all tests
./gradlew test

# Run specific test
./gradlew test --tests UserServiceTest
```

## Production Deployment

1. **Build JAR**
```bash
./gradlew build
```

2. **Run với production profile**
```bash
java -jar build/libs/demo-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod
```

3. **Docker** (optional)
```dockerfile
FROM openjdk:17-jre-slim
COPY build/libs/demo-0.0.1-SNAPSHOT.jar app.jar
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## Monitoring

- Health checks: `/actuator/health`
- Metrics: `/actuator/metrics`
- Application logs với structured logging

## Best Practices

- Sử dụng DTOs cho input/output
- Validation ở controller layer
- Business logic ở service layer
- Event-driven cho side effects
- Consistent error handling
- Structured logging
- API versioning (/api/v1/)

## Contribution

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request 