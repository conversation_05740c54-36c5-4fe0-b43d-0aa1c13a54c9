#!/bin/bash

# Script to migrate all handlers to use BaseApiHandler
# This removes the need for manual getRequestType() implementation

echo "Migrating handlers to use BaseApiHandler..."

# Find all handler files
HANDLER_FILES=$(find src/main/java -name "*ApiHandler.java" -not -name "BaseApiHandler.java")

for file in $HANDLER_FILES; do
    echo "Processing: $file"
    
    # Replace ApiHandler import with BaseApiHandler
    sed -i '' 's/import com\.thanhnb\.demo\.handler\.bus\.ApiHandler;/import com.thanhnb.demo.handler.bus.BaseApiHandler;/' "$file"
    
    # Replace implements ApiHandler with extends BaseApiHandler
    sed -i '' 's/implements ApiHandler</extends BaseApiHandler</' "$file"
    
    # Remove getRequestType method (find and remove the entire method)
    # This is a more complex sed operation to remove multi-line method
    perl -i -pe 'BEGIN{undef $/;} s/\s*@Override\s*public Class<[^>]+> getRequestType\(\) \{\s*return [^;]+;\s*\}//smg' "$file"
    
    echo "Migrated: $file"
done

echo "Migration completed!"
echo "Please review the changes and test the application."
